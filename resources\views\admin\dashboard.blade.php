@extends('layouts.admin')

@section('title', 'Dashboard')

@php
$pageTitle = 'Dashboard';
$pageDescription = 'Welcome to your admin dashboard';
$breadcrumbs = [
['title' => 'Dashboard', 'url' => '#']
];
@endphp

@section('content')
<!-- Stats Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Total Users -->
    <div class="material-card p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <i class="material-icons text-blue-600">people</i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Total Users</p>
                <p class="text-2xl font-semibold text-gray-900">{{ number_format($stats['total_users']) }}</p>
                <p class="text-sm text-green-600">{{ $stats['new_users_this_month'] }} new this month</p>
            </div>
        </div>
    </div>

    <!-- Revenue (now Total News) -->
    <div class="material-card p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <i class="material-icons text-green-600">article</i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Total News</p>
                <p class="text-2xl font-semibold text-gray-900">{{ number_format($stats['total_news'] ?? 0) }}</p>
                <p class="text-sm text-green-600">{{ $stats['news_integration_processed'] ?? 0 }} new this month</p>
            </div>
        </div>
    </div>

    <!-- Orders (now Pending News) -->
    <div class="material-card p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                    <i class="material-icons text-orange-600">hourglass_empty</i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">{{ $stats['pending_news_label'] ?? 'Pending News' }}</p>
                <p class="text-2xl font-semibold text-gray-900">{{ number_format($stats['news_integration_pending'] ?? 0) }}</p>
                <p class="text-sm text-red-600">{{ $stats['news_integration_pending'] ?? 0 }} {{ $stats['pending_news_change_label'] ?? 'change from last month' }}</p>
            </div>
        </div>
    </div>

    <!-- Conversion Rate (now Total Published News) -->
    <div class="material-card p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                    <i class="material-icons text-purple-600">check_circle</i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Total Published News</p>
                <p class="text-2xl font-semibold text-gray-900">{{ number_format($stats['published_news'] ?? 0) }}</p>
                <p class="text-sm text-green-600">{{ $stats['news_integration_generated'] ?? 0 }} change from last month</p>
            </div>
        </div>
    </div>
</div>
<div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
    <!-- News Integration (from LatestNewsTitle) -->
    <div class="material-card p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-cyan-100 rounded-lg flex items-center justify-center">
                    <i class="material-icons text-cyan-600">sync</i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">News Integration</p>
                <p class="text-2xl font-semibold text-gray-900">{{ number_format($stats['news_integration_total'] ?? 0) }}</p>
                <div class="flex flex-col sm:flex-row sm:space-x-2 mt-1">
                    <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 mb-1 sm:mb-0">
                        <i class="material-icons text-xs mr-1">check_circle</i> Processed: {{ number_format($stats['news_integration_processed'] ?? 0) }}
                    </span>
                    <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mb-1 sm:mb-0">
                        <i class="material-icons text-xs mr-1">done_all</i> Generated: {{ number_format($stats['news_integration_generated'] ?? 0) }}
                    </span>
                    <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                        <i class="material-icons text-xs mr-1">pending</i> Pending: {{ number_format($stats['news_integration_pending'] ?? 0) }}
                    </span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts and Tables Row -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
    <!-- Recent Activity -->
    <div class="material-card">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Recent Activity</h3>
        </div>
        <div class="p-6">
            <div class="space-y-4">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <i class="material-icons text-blue-600 text-sm">person_add</i>
                    </div>
                    <div class="flex-1">
                        <p class="text-sm text-gray-900">New user registered</p>
                        <p class="text-xs text-gray-500">2 minutes ago</p>
                    </div>
                </div>

                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <i class="material-icons text-green-600 text-sm">shopping_cart</i>
                    </div>
                    <div class="flex-1">
                        <p class="text-sm text-gray-900">New order received</p>
                        <p class="text-xs text-gray-500">5 minutes ago</p>
                    </div>
                </div>

                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                        <i class="material-icons text-orange-600 text-sm">payment</i>
                    </div>
                    <div class="flex-1">
                        <p class="text-sm text-gray-900">Payment processed</p>
                        <p class="text-xs text-gray-500">10 minutes ago</p>
                    </div>
                </div>

                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                        <i class="material-icons text-purple-600 text-sm">feedback</i>
                    </div>
                    <div class="flex-1">
                        <p class="text-sm text-gray-900">New review submitted</p>
                        <p class="text-xs text-gray-500">15 minutes ago</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="material-card">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Quick Actions</h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-2 gap-4">
                <a href="{{ route('admin.users.create') }}" class="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                    <i class="material-icons text-blue-600 mb-2">person_add</i>
                    <span class="text-sm font-medium text-gray-900">Add User</span>
                </a>

                <a href="#" class="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                    <i class="material-icons text-green-600 mb-2">add_box</i>
                    <span class="text-sm font-medium text-gray-900">New Product</span>
                </a>

                <a href="#" class="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                    <i class="material-icons text-orange-600 mb-2">campaign</i>
                    <span class="text-sm font-medium text-gray-900">Send Newsletter</span>
                </a>

                <a href="#" class="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                    <i class="material-icons text-purple-600 mb-2">analytics</i>
                    <span class="text-sm font-medium text-gray-900">View Reports</span>
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Recent Orders Table -->
<div class="material-card">
    <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900">Recent Orders</h3>
            <a href="#" class="text-sm text-blue-600 hover:text-blue-800">View all</a>
        </div>
    </div>
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order ID</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">#12345</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">John Doe</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">$299.99</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                            Completed
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-15</td>
                </tr>
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">#12344</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Jane Smith</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">$149.99</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                            Pending
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-14</td>
                </tr>
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">#12343</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Bob Johnson</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">$89.99</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                            Processing
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-13</td>
                </tr>
            </tbody>
        </table>
    </div>
</div>
@endsection