<?php $__env->startSection('title', 'Laravel Log Viewer'); ?>



<?php $__env->startSection('page-header'); ?>
<div class="flex items-center justify-between">
    <div>
        <h1 class="text-3xl font-semibold text-gray-900"><?php echo e($pageTitle); ?></h1>
        <p class="mt-2 text-gray-600"><?php echo e($pageDescription); ?></p>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<!-- Log DataTable styled like Recent Orders -->
<div class="material-card">
    <div class="p-6 border-b border-gray-200">
        <div class="flex flex-col  md:flex-row md:items-center md:justify-between gap-4 w-full">
            <div class="flex flex-col sm:flex-row items-start sm:items-center gap-2 w-full ">
                <label for="log-month" class="text-sm font-medium text-gray-700 whitespace-nowrap mb-1 sm:mb-0">Filter by Month:</label>
                <select id="log-month" class="material-input w-full min-w-[200px]">
                    <?php $__currentLoopData = $monthOptions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $opt): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($opt['value']); ?>" <?php if($opt['value']===$currentMonth): ?> selected <?php endif; ?>><?php echo e($opt['label']); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
                <button id="delete-month-log" class="whitespace-nowrap material-button material-button-sm material-button-danger flex items-center ml-0 sm:ml-2 w-full sm:w-auto mt-2 sm:mt-0">
                    <i class="material-icons text-sm mr-1">delete</i> Delete Month Log
                </button>
            </div>
            <div class="flex justify-end w-full  mt-2 md:mt-0">
                <button id="refresh-table" class="material-button material-button-sm material-button-secondary flex items-center w-full sm:w-auto">
                    <i class="material-icons text-sm mr-1">refresh</i> Refresh
                </button>
            </div>
        </div>
    </div>
    <div class="overflow-x-auto">
        <table id="logTable" class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SR No</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Environment</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Level</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Message</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
            </tbody>
        </table>
    </div>
    <div class="px-6 py-4 bg-gray-50 border-t border-gray-200">
        <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between">
            <div class="flex items-center space-x-2 mb-4 sm:mb-0">
                <span class="text-sm text-gray-700">Show</span>
                <select id="page-length" class="w-16">
                    <option value="10">10</option>
                    <option value="25">25</option>
                    <option value="50">50</option>
                    <option value="100">100</option>
                </select>
                <span class="text-sm text-gray-700">entries</span>
            </div>
            <div id="table-info" class="text-sm text-gray-700 mb-4 sm:mb-0"></div>
            <div id="logs-pagination" class="w-full sm:w-auto"></div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/dataTables.tailwindcss.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.dataTables.min.css">
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.7/js/dataTables.tailwindcss.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
<script>
    $(document).ready(function() {
        // Set default month filter
        let defaultMonth = $('#log-month').val();
        let table = $('#logTable').DataTable({
            serverSide: true,
            processing: true,
            ajax: {
                url: "<?php echo e(route('admin.logs.fetch')); ?>",
                type: 'GET',
                data: function(d) {
                    d.month = $('#log-month').val();
                }
            },
            columns: [{
                    data: null,
                    name: 'srno',
                    orderable: false,
                    searchable: false,
                    render: function(data, type, row, meta) {
                        return meta.row + meta.settings._iDisplayStart + 1;
                    },
                    className: 'px-6 py-4 whitespace-nowrap text-sm text-gray-900'
                },
                {
                    data: 'date',
                    name: 'date',
                    orderable: true,
                    searchable: true
                },
                {
                    data: 'env',
                    name: 'env',
                    orderable: true,
                    searchable: true
                },
                {
                    data: 'level',
                    name: 'level',
                    orderable: true,
                    searchable: true
                },
                {
                    data: 'message',
                    name: 'message',
                    orderable: false,
                    searchable: true,
                    render: function(data, type, row, meta) {
                        if (!data) return '';
                        let firstLine = data.split(/\r?\n/)[0];
                        if (data.split(/\r?\n/).length > 1) {
                            firstLine += ' <span class="text-xs text-blue-500">[more]</span>';
                        }
                        return '<span class="truncate inline-block max-w-xs align-middle" title="' + firstLine + '">' + firstLine + '</span>';
                    },
                    className: 'px-6 py-4 whitespace-nowrap text-sm text-gray-900'
                },
                {
                    data: null,
                    name: 'actions',
                    orderable: false,
                    searchable: false,
                    render: function(data, type, row, meta) {
                        const safeId = `log-msg-${meta.row}`;
                        const safeMessage = $('<div/>').text(row.message).html();
                        return `
                            <button class="view-log-btn text-blue-600 hover:text-blue-800 cursor-pointer" 
                                    title="View Full Log" 
                                    data-target="${safeId}">
                                <i class="material-icons">visibility</i>
                            </button>
                            <textarea id="${safeId}" class="hidden">${safeMessage}</textarea>
                        `;
                    },
                    className: 'px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-center'
                }
            ],
            order: [
                [1, 'desc']
            ],
            pageLength: 10,
            lengthMenu: [
                [10, 25, 50, 100],
                [10, 25, 50, 100]
            ],
            language: {
                processing: '<div>Loading data...</div>',
                paginate: {
                    previous: '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-chevron-left"><polyline points="15 18 9 12 15 6"></polyline></svg>',
                    next: '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-chevron-right"><polyline points="9 18 15 12 9 6"></polyline></svg>'
                },
                info: "Showing _START_ to _END_ of _TOTAL_ entries"
            },
            dom: 'rtip',
            responsive: false,
            drawCallback: function(settings) {
                $('#logs-pagination').html($('.dataTables_paginate'));
                $('#table-info').html($('.dataTables_info'));
                $('.paginate_button').addClass('custom-paginate-button');
                // Rebind modal open event after pagination
                $('.view-log-btn').off('click').on('click', function() {
                    const targetId = $(this).data('target');
                    const message = $(`#${targetId}`).val();
                    let modalHtml = `
                    <div id="log-modal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40">
                        <div class="bg-white rounded-xl shadow-2xl max-w-3xl w-full p-0 relative">
                            <div class="flex items-center justify-between px-6 py-4 border-b border-gray-200 bg-blue-50 rounded-t-xl">
                                <h2 class="text-lg font-semibold text-blue-900 flex items-center">
                                    <i class="material-icons mr-2 text-blue-600">bug_report</i> Log Details
                                </h2>
                                <button id="close-log-modal" class="text-gray-500 hover:text-gray-700"><i class="material-icons">close</i></button>
                            </div>
                            <div class="px-6 py-4">
                                <div class="mb-4">
                                    <span class="inline-block px-3 py-1 text-xs font-semibold rounded-full bg-gray-200 text-gray-800 mb-2">Full Error Message</span>
                                    <pre class="bg-gray-100 rounded-lg p-4 text-sm text-red-800 overflow-x-auto whitespace-pre-wrap border border-red-200 shadow-inner" style="max-height: 400px;">${$('<div/>').text(message).html()}</pre>
                                </div>
                            </div>
                        </div>
                    </div>`;
                    $('body').append(modalHtml);
                });
            }
        });
        $('#search-input').on('keyup', function() {
            table.search($(this).val()).draw();
        });
        $('#page-length').on('change', function() {
            table.page.len(parseInt($(this).val())).draw();
        });
        $('#refresh-table').on('click', function() {
            table.ajax.reload();
        });
        $('#log-month').on('change', function() {
            table.ajax.reload();
        });
        $('#delete-month-log').on('click', function() {
            let month = $('#log-month').val();
            if (!month) {
                alert('Please select a month to delete.');
                return;
            }
            if (!confirm('Are you sure you want to delete the log file for ' + $('#log-month option:selected').text() + '? This action cannot be undone.')) {
                return;
            }
            $.ajax({
                url: "<?php echo e(route('admin.logs.delete-monthly')); ?>",
                type: 'POST',
                data: {
                    month: month,
                    _token: '<?php echo e(csrf_token()); ?>'
                },
                success: function(res) {
                    alert('Log file deleted successfully.');
                    $('#refresh-table').click();
                },
                error: function(xhr) {
                    alert(xhr.responseJSON && xhr.responseJSON.error ? xhr.responseJSON.error : 'Failed to delete log file.');
                }
            });
        });
    });
    $(document).on('click', '#close-log-modal', function() {
        $('#log-modal').remove();
    });
    // Close modal on ESC key
    $(document).on('keydown', function(e) {
        if (e.key === 'Escape' && $('#log-modal').length) {
            $('#log-modal').remove();
        }
    });
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\projects\client-admin-panel\resources\views/admin/logs/index.blade.php ENDPATH**/ ?>