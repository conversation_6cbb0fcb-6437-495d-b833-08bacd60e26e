# Gemini API Key Dynamic Management

This feature allows you to manage your Gemini AI API key dynamically through the admin settings panel. When you update the API key, it will be saved both in the database (encrypted) and updated in your `.env` file for immediate use.

## Features

- **Dynamic Updates**: Update the Gemini API key through the admin panel
- **Dual Storage**: Saves to both database (encrypted) and `.env` file
- **Real-time Status**: Shows current configuration status for both locations
- **API Key Testing**: Test your API key before saving
- **Security**: API keys are encrypted when stored in the database
- **Password Toggle**: Hide/show API key for security

## How to Use

### 1. Access Settings
1. Log in to your admin panel
2. Navigate to **Settings** from the admin menu
3. Scroll down to the **API Settings** section

### 2. Update Gemini API Key
1. In the **API Settings** section, find the "Gemini AI API Key" field
2. Enter your new API key in the password field
3. Optionally, test the API key using the "Test API Key" feature
4. Click **Save Settings** to update both database and `.env` file

### 3. Test API Key (Optional)
1. In the "Test API Key" section, enter your API key
2. Click **Test API Key** button
3. The system will validate the key format and provide feedback

### 4. Check Status
The **API Key Status** section shows:
- **Database**: Whether the key is configured in the database
- **.env File**: Whether the key is configured in the `.env` file

Both should show "Configured" for the API to work properly.

## Technical Details

### Database Storage
- API keys are stored in the `settings` table
- Group: `api`
- Key: `gemini_api_key`
- Encryption: Yes (using Laravel's built-in encryption)

### Environment File
- Variable name: `GEMINI_API_KEY`
- Location: `.env` file in project root
- Used by: `config/services.php` → `services.gemini.api_key`

### Security Features
- Database values are encrypted using Laravel's Crypt facade
- Password fields are hidden by default with toggle visibility
- API key testing validates format without exposing the key
- Proper validation and error handling

## Error Handling

### Common Issues
1. **"Failed to update .env file"**
   - Check file permissions on `.env` file
   - Ensure the file exists and is writable

2. **"API key appears to be too short"**
   - Verify you're using a valid Gemini API key
   - Check for extra spaces or characters

3. **Settings not taking effect**
   - Clear application cache: `php artisan config:clear`
   - Restart your web server if needed

### File Permissions
Ensure your `.env` file has proper write permissions:
```bash
chmod 644 .env
```

## Code Structure

### New Files
- `app/Services/EnvManager.php` - Handles `.env` file operations
- `GEMINI_API_SETUP.md` - This documentation

### Modified Files
- `app/Http/Controllers/Admin/SettingsController.php` - Added API key handling
- `resources/views/admin/settings/index.blade.php` - Added UI components
- `routes/web.php` - Added test API route

### Key Methods
- `EnvManager::updateEnv($key, $value)` - Updates environment variables
- `EnvManager::getEnvValue($key)` - Reads environment variables
- `SettingsController::testGeminiApi()` - Tests API key validity

## Usage in Code

After setting up the API key, you can use it in your application:

```php
// Get from config (recommended)
$apiKey = config('services.gemini.api_key');

// Get from database setting
$apiKey = \App\Models\Setting::get('gemini_api_key');

// Get from .env directly
$apiKey = \App\Services\EnvManager::getEnvValue('GEMINI_API_KEY');
```

## Troubleshooting

1. **Clear caches** after making changes:
   ```bash
   php artisan config:clear
   php artisan cache:clear
   ```

2. **Check logs** for detailed error messages:
   ```bash
   tail -f storage/logs/laravel.log
   ```

3. **Verify file permissions** on `.env` file

4. **Test the API key** using the built-in test function before saving

## Security Notes

- API keys are encrypted in the database using Laravel's encryption
- The `.env` file should never be committed to version control
- Use proper file permissions on your `.env` file
- Regularly rotate your API keys for security
- The test function only validates format, not actual API connectivity

## Support

If you encounter issues:
1. Check the error messages in the admin panel
2. Review the application logs
3. Verify file permissions
4. Ensure your Gemini API key is valid and active
