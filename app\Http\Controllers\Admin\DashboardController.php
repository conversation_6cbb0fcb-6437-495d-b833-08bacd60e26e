<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\News;
use App\Models\LatestNewsTitle;
use Illuminate\Http\Request;

class DashboardController extends Controller
{
    /**
     * Show the admin dashboard.
     */
    public function index()
    {
        // Get some basic statistics for the dashboard
        $stats = [
            'total_users' => User::count(),
            'new_users_today' => User::whereDate('created_at', today())->count(),
            'new_users_this_month' => User::whereMonth('created_at', now()->month)
                                         ->whereYear('created_at', now()->year)
                                         ->count(),
            // News stats
            'total_news' => News::count(),
            'news_this_month' => News::whereMonth('created_at', now()->month)
                                    ->whereYear('created_at', now()->year)
                                    ->count(),
            'pending_news' => News::where('approval_status', 'pending')->count(),
            'pending_news_change' => 0, // You can implement month-over-month logic if needed
            'pending_news_label' => 'Pending News',
            'pending_news_change_label' => 'change from last month',
            'published_news' => News::published()->count(),
            'published_news_change' => 0, // You can implement month-over-month logic if needed
            // News Integration stats
            'news_integration_total' => LatestNewsTitle::count(),
            'news_integration_processed' => LatestNewsTitle::where('is_processed', true)->count(),
            'news_integration_generated' => LatestNewsTitle::where('is_generated', true)->count(),
            'news_integration_pending' => LatestNewsTitle::readyForGeneration()->count(),
        ];

        // Get recent users
        $recent_users = User::latest()->take(5)->get();

        return view('admin.dashboard', compact('stats', 'recent_users'));
    }
}
